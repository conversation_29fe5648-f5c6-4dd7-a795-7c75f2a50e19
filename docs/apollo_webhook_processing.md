# Apollo Webhook 数据处理完整方案

## 概述

本文档描述了完整的 Apollo webhook 数据处理流程，包括数据转换、LLM 分析、CRM 更新和错误处理。

## 核心功能

### 1. 数据转换方法

#### 完整转换（推荐）
```python
from zoho.data_transform import transform_apollo_webhook_data_to_crm_contact_data_complete

# 包含 LLM 分析的完整转换
crm_data = await transform_apollo_webhook_data_to_crm_contact_data_complete(apollo_webhook_data)
```

**特点：**
- 包含所有 CRM 字段（30+ 字段）
- 使用 LLM 分析优化非直接映射字段
- 智能推理 Function_Type、Associated_Account_Type、Associated_Industry 等
- 生成优化的推荐理由（Rec_Reason）
- 适合完整的联系人数据处理

#### 简化转换
```python
from zoho.data_transform import transform_apollo_webhook_data_to_crm_contact_info_simple

# 仅直接映射字段的快速转换
crm_info = transform_apollo_webhook_data_to_crm_contact_info_simple(apollo_webhook_data)
```

**特点：**
- 仅包含可直接映射的字段（10+ 字段）
- 处理速度快，无 LLM 调用
- 适合快速更新场景

### 2. Webhook 处理端点

```
POST /api/sales-agent-webhook/apollo/{request_id}
```

**处理流程：**
1. 验证 webhook 数据
2. Apollo 数据转换为 CRM 格式
3. 更新 CRM 联系人信息
4. 返回详细的处理结果

## 字段映射详情

### 直接映射字段

| CRM 字段 | Apollo 字段 | 处理方式 |
|---------|------------|----------|
| `First_Name` | `first_name` | 直接映射 |
| `Last_Name` | `last_name` | 直接映射 |
| `Email` | `email` | 直接映射 |
| `Title` | `title` | 直接映射 |
| `LinkedIn` | `linkedin_url` | 直接映射 |
| `Facebook` | `facebook_url` | 直接映射 |
| `Twitter` | `twitter_url` | 提取用户名 |
| `Mobile` | `phone_numbers[type_cd=mobile]` | 解析数组 |
| `Phone` | `phone_numbers[type_cd!=mobile]` | 解析数组 |
| `Department` | `departments` | 映射转换 |

### LLM 分析字段

| CRM 字段 | 分析依据 | 输出范围 |
|---------|----------|----------|
| `Function_Type` | 职位、部门、行业 | Business Entry/Management/Senior Management, Technical Entry/Management/Senior Management |
| `Associated_Account_Type` | 公司类型、业务模式 | EU, OEM, SI, DIS, Operator, Sales Rep, Partner, Others, Telecom Operator, IoT Platform, IoT Partner, MSP |
| `Associated_Industry` | 行业分析 | Energy, Industry, IWOS, Commerce, City, ICT, Mobility, Smart Vending, Others, Telecom Operator |
| `Region` | 地理位置 | EMEA, NAM, LATAM, APAC |
| `Rec_Reason` | 综合分析 | 英文推荐理由 |

### 固定值字段

| CRM 字段 | 固定值 |
|---------|--------|
| `Currency` | "USD" |
| `Labels` | "Sales Agent" |
| `Lead_Source` | "AI Research" |
| `Exchange_Rate` | 1 |

## 使用示例

### 1. Webhook 处理示例

```python
@router.post("/api/sales-agent-webhook/apollo/{request_id}")
async def handle_webhook(request_id: str, request: dict):
    # 获取联系人数据
    people_list = request.get("people", [])
    apollo_webhook_data = people_list[0]
    
    # 完整转换
    crm_data = await transform_apollo_webhook_data_to_crm_contact_data_complete(apollo_webhook_data)
    
    # 更新 CRM
    await update_contact_by_id(contact_id, crm_data)
    
    return {"success": True, "updated_fields": list(crm_data.keys())}
```

### 2. 错误处理示例

```python
try:
    crm_data = await transform_apollo_webhook_data_to_crm_contact_data_complete(apollo_data)
except Exception as e:
    logger.error(f"数据转换失败: {e}")
    return {"success": False, "error": "DATA_TRANSFORMATION_FAILED"}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "request_id": "req_123456",
  "contact_id": "contact_789",
  "message": "成功更新联系人 contact_789 信息，更新了 15 个字段",
  "updated_fields": ["First_Name", "Last_Name", "Email", "Title", "Mobile", "Phone", "LinkedIn", "Function_Type", "Associated_Industry", "Region", "Rec_Reason"],
  "processing_steps": ["验证 webhook 数据", "Apollo 数据转换为 CRM 格式", "更新 CRM 联系人信息", "CRM 更新完成", "更新扩充状态: success"]
}
```

### 错误响应
```json
{
  "success": false,
  "request_id": "req_123456",
  "contact_id": "contact_789",
  "message": "Apollo 返回状态不是 success: failed",
  "error_details": "APOLLO_STATUS_NOT_SUCCESS",
  "updated_fields": [],
  "processing_steps": ["验证 webhook 数据"]
}
```

## 错误类型

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| `INVALID_REQUEST_ID` | 请求 ID 不存在 | 检查请求 ID 是否正确 |
| `EMPTY_PEOPLE_LIST` | 联系人列表为空 | 检查 Apollo 返回数据 |
| `APOLLO_STATUS_NOT_SUCCESS` | Apollo 状态不是成功 | 检查 Apollo 处理状态 |
| `DATA_TRANSFORMATION_FAILED` | 数据转换失败 | 检查输入数据格式 |
| `NO_VALID_UPDATE_FIELDS` | 没有有效更新字段 | 检查数据内容 |
| `NO_PHONE_INFO` | 没有电话信息 | 检查 phone_numbers 数组 |
| `CRM_UPDATE_FAILED` | CRM 更新失败 | 检查 CRM API 连接 |
| `UNEXPECTED_ERROR` | 意外错误 | 查看详细日志 |

## 性能优化

1. **LLM 调用优化**：使用缓存机制避免重复分析相似数据
2. **批量处理**：支持批量转换多个联系人
3. **异步处理**：所有 I/O 操作使用异步方式
4. **错误恢复**：LLM 分析失败时使用默认值，不中断流程

## 监控和日志

- 所有操作都有详细的日志记录
- 包含 "Sales Agent" 前缀便于日志过滤
- 记录处理步骤和耗时信息
- 错误信息包含堆栈跟踪

## 测试

### 1. 数据转换测试
```bash
cd /path/to/sales-agent
python -m zoho.data_transform
```

### 2. Webhook 端点测试
```bash
cd /path/to/sales-agent
python tests/test_apollo_webhook.py
```

测试将验证：
- 基本字段映射
- 电话号码解析
- 部门映射转换
- LLM 分析功能
- 错误处理机制
- Webhook 端点响应
- 各种错误场景

## 部署注意事项

1. **环境变量**：确保配置了必要的 API 密钥
2. **LLM 模型**：确保 OpenAI API 可用
3. **CRM 连接**：确保 Zoho CRM API 连接正常
4. **日志配置**：配置适当的日志级别
5. **错误监控**：建议配置错误监控和告警

## 总结

这个完整的 Apollo webhook 处理方案提供了：

✅ **完整的数据映射**：支持 30+ CRM 字段的智能映射
✅ **LLM 智能分析**：自动推理复杂字段值
✅ **robust 错误处理**：详细的错误分类和客户端反馈
✅ **灵活的转换选项**：支持快速和完整两种转换模式
✅ **完整的测试覆盖**：包含单元测试和集成测试
✅ **详细的日志记录**：便于问题排查和监控
✅ **标准化响应格式**：便于客户端处理结果
