#!/usr/bin/env python3
"""
Apollo Webhook 测试脚本
用于测试 Apollo webhook 数据处理的完整流程
"""

import asyncio
import json
import httpx
from loguru import logger

# 测试用的 Apollo webhook 数据
SAMPLE_APOLLO_WEBHOOK_DATA = {
    "people": [
        {
            "status": "success",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "title": "VP of Engineering",
            "linkedin_url": "http://www.linkedin.com/in/john-smith-tech",
            "twitter_url": "https://twitter.com/johnsmith_tech",
            "facebook_url": "https://www.facebook.com/johnsmith",
            "country": "United States",
            "departments": "['information_technology', 'engineering_technical']",
            "subdepartments": "['software_engineering']",
            "seniority": "vp",
            "thinking": "<PERSON> is VP of Engineering at TechCorp, responsible for leading the engineering team and making technology decisions. His role involves evaluating new technologies and solutions, making him an ideal contact for IoT and edge computing products that can enhance their technical infrastructure.",
            "priority": "high",
            "phone_numbers": [
                {
                    "raw_number": "******-123-4567",
                    "sanitized_number": "+15551234567",
                    "type_cd": "mobile",
                    "status": "valid_number"
                },
                {
                    "raw_number": "******-987-6543",
                    "sanitized_number": "+15559876543",
                    "type_cd": "work",
                    "status": "valid_number"
                }
            ],
            "organization": {
                "id": "org_123456",
                "name": "TechCorp Inc.",
                "website_url": "https://www.techcorp.com",
                "linkedin_url": "http://www.linkedin.com/company/techcorp"
            }
        }
    ]
}

# 测试失败场景的数据
FAILED_APOLLO_WEBHOOK_DATA = {
    "people": [
        {
            "status": "failed",
            "error": "Unable to enrich contact information"
        }
    ]
}

EMPTY_APOLLO_WEBHOOK_DATA = {
    "people": []
}


async def test_webhook_endpoint(base_url: str, request_id: str, webhook_data: dict):
    """
    测试 webhook 端点
    
    Args:
        base_url: 服务器基础 URL
        request_id: 请求 ID
        webhook_data: webhook 数据
    """
    url = f"{base_url}/api/sales-agent-webhook/apollo/{request_id}"
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            logger.info(f"发送 webhook 请求到: {url}")
            logger.info(f"请求数据: {json.dumps(webhook_data, indent=2)}")
            
            response = await client.post(url, json=webhook_data)
            
            logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                if response_data.get("success"):
                    logger.success("✅ Webhook 处理成功!")
                    logger.info(f"更新的字段: {response_data.get('updated_fields', [])}")
                    logger.info(f"处理步骤: {response_data.get('processing_steps', [])}")
                else:
                    logger.error("❌ Webhook 处理失败!")
                    logger.error(f"错误信息: {response_data.get('message', 'Unknown error')}")
                    logger.error(f"错误详情: {response_data.get('error_details', 'No details')}")
            else:
                logger.error(f"HTTP 错误: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                
        except Exception as e:
            logger.error(f"请求失败: {e}")


async def test_data_transformation():
    """
    测试数据转换功能
    """
    logger.info("=== 测试数据转换功能 ===")
    
    try:
        from zoho.data_transform import (
            transform_apollo_webhook_data_to_crm_contact_data_complete,
            transform_apollo_webhook_data_to_crm_contact_info_simple
        )
        
        apollo_data = SAMPLE_APOLLO_WEBHOOK_DATA["people"][0]
        
        # 测试完整转换
        logger.info("测试完整转换（带 LLM 分析）...")
        complete_data = await transform_apollo_webhook_data_to_crm_contact_data_complete(apollo_data)
        logger.success(f"✅ 完整转换成功，包含 {len(complete_data)} 个字段")
        
        # 测试简化转换
        logger.info("测试简化转换（不带 LLM 分析）...")
        simple_data = transform_apollo_webhook_data_to_crm_contact_info_simple(apollo_data)
        logger.success(f"✅ 简化转换成功，包含 {len(simple_data)} 个字段")
        
        # 比较两种转换结果
        logger.info("=== 转换结果比较 ===")
        logger.info(f"完整转换字段: {list(complete_data.keys())}")
        logger.info(f"简化转换字段: {list(simple_data.keys())}")
        
        common_fields = set(complete_data.keys()) & set(simple_data.keys())
        logger.info(f"共同字段 ({len(common_fields)}): {list(common_fields)}")
        
        complete_only = set(complete_data.keys()) - set(simple_data.keys())
        logger.info(f"仅完整转换包含 ({len(complete_only)}): {list(complete_only)}")
        
    except Exception as e:
        logger.error(f"❌ 数据转换测试失败: {e}")


async def main():
    """
    主测试函数
    """
    logger.info("🚀 开始 Apollo Webhook 测试")
    
    # 配置
    BASE_URL = "http://localhost:8000"  # 根据实际服务器地址修改
    TEST_REQUEST_ID = "test_request_123"
    
    # 测试 1: 数据转换功能
    await test_data_transformation()
    
    print("\n" + "="*50)
    
    # 测试 2: 成功场景的 webhook
    logger.info("=== 测试成功场景的 Webhook ===")
    await test_webhook_endpoint(BASE_URL, TEST_REQUEST_ID, SAMPLE_APOLLO_WEBHOOK_DATA)
    
    print("\n" + "="*50)
    
    # 测试 3: 失败场景的 webhook
    logger.info("=== 测试失败场景的 Webhook ===")
    await test_webhook_endpoint(BASE_URL, "test_failed_123", FAILED_APOLLO_WEBHOOK_DATA)
    
    print("\n" + "="*50)
    
    # 测试 4: 空数据场景的 webhook
    logger.info("=== 测试空数据场景的 Webhook ===")
    await test_webhook_endpoint(BASE_URL, "test_empty_123", EMPTY_APOLLO_WEBHOOK_DATA)
    
    print("\n" + "="*50)
    
    # 测试 5: 无效请求 ID
    logger.info("=== 测试无效请求 ID ===")
    await test_webhook_endpoint(BASE_URL, "invalid_request_id", SAMPLE_APOLLO_WEBHOOK_DATA)
    
    logger.info("🏁 测试完成")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
