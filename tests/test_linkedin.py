from agent.tools.linkedin import rapidapi


async def test_search_people():
    result = await rapidapi.search_people(keywords="<PERSON>", title="R&D CTO Office")
    print(result)
    data = result.get("data")
    assert data is not None
    assert result.get("error") is None
    assert len(data) > 0
    assert data[0].get("fullName") is not None
    assert data[0].get("headline") is not None
    assert data[0].get("summary") is not None
    assert data[0].get("profilePicture") is not None
    assert data[0].get("location") is not None
    assert data[0].get("profileURL") is not None
