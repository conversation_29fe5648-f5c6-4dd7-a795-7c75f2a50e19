import asyncio
import json

from fastapi import APIRouter, HTTPException  # 修改: 添加 Query, Request
from fastapi.responses import StreamingResponse  # 修改: 添加 StreamingResponse
from loguru import logger
from taskiq import AsyncTaskiqTask

from apollo.apollo_api import enrich_people_phone
from server.common_types import BatchAccountTaskRequest, CompanyInfo, StreamMessage, TaskType
from server.contact_enrichment_requests import contact_enrichment_requests
from server.task.task_manager import task_manager
from zoho.contacts_api import get_contact_by_id, update_contact_by_id

# 创建路由器
router = APIRouter()


@router.post("/api/sales-agent/detect-contacts/start")
async def start_detect_contacts_task(request: CompanyInfo):
    """接收初始请求的端点"""
    data = request.model_dump()

    logger.info(f"开始处理请求: {data}")
    # get account_id from data
    account_id = request.account_id
    custom_prompts = request.custom_prompts
    owner_id = request.owner_id
    current_user = request.current_user
    request_headers = request.request_headers
    if not account_id:
        logger.warning("请求中缺少 account_id")
        return {"error": "请求中缺少 account_id"}

    # 检查数据库中是否存在任务数据
    existing_tasks = await task_manager.list_incompleted_tasks(
        task_type=TaskType.DETECT_CONTACTS.value, tags={"account_id": account_id}
    )

    if existing_tasks:
        # 如果存在未完成的任务，返回错误
        logger.warning(f"账户 {account_id} 已有未完成的任务: {existing_tasks}")
        if existing_tasks[0]:
            return {
                "task_id": existing_tasks[0],
                "messages": f"账户 {account_id} 已有未完成的任务，请等待当前任务完成或取消后再试",
                "notes": "existing_task",
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "type": "error",
                    "messages": f"账户 {account_id} 已有未完成的任务，请等待当前任务完成或取消后再试",
                },
            )
    try:
        # 检查任务状态
        task: AsyncTaskiqTask[str] = await task_manager.add_detect_contact_task(
            account_id=account_id,
            custom_prompts=custom_prompts,
            owner_id=owner_id,
            current_user=current_user,
            request_headers=request_headers,
        )
        return {"task_id": task.task_id, "status": "running"}
    except Exception as e:
        logger.error(f"提交新任务失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=400,
            detail={"type": "error", "messages": f"提交新任务失败: {str(e)}"},
        )


@router.get("/api/sales-agent/tasks/{task_id}/stream")
async def get_task_stream(task_id: str):
    """SSE连接端点，根据 task_id 执行任务并流式传输事件"""

    def _build_stream_message(message: StreamMessage) -> str:
        return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"

    async def event_generator():
        try:
            async for message in task_manager.subscribe(task_id):
                yield _build_stream_message(message)
        except asyncio.CancelledError:
            logger.info(f"Stream for task {task_id} cancelled")
            return
        except Exception as e:
            logger.error(f"为会话 {task_id} 创建流时出错: {e}", exc_info=True)
            message = StreamMessage.from_exception(e)
            yield _build_stream_message(message)

    return StreamingResponse(
        content=event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "X-Session-ID": task_id,
        },
    )


@router.get("/api/sales-agent/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """
    根据 task_id 获取任务结果
    """
    await task_manager.cancel_task_by_id(task_id)
    return {"message": "任务已取消"}


@router.get("/api/sales-agent/tasks/{task_id}")
async def get_task_data_by_task_id(task_id: str):
    """
    根据 task_id 获取任务数据
    """
    result = await task_manager.get_task_data(task_id=task_id)
    if result is None:
        raise HTTPException(
            status_code=404,
            detail={"error": "not_found", "messages": "Task result not found"},
        )
    return result


@router.get("/api/sales-agent/account/{account_id}/task")
async def get_account_task_by_account_id(account_id: str):
    """
    根据 account_id 获取任务
    """
    result = await task_manager.get_task_result_by_account_id(account_id=account_id)
    if result is None:
        raise HTTPException(
            status_code=404,
            detail={"error": "not_found", "messages": "Task result not found"},
        )
    return result


@router.post("/api/sales-agent/accounts/tasks")
async def get_account_tasks_by_account_ids(request: BatchAccountTaskRequest):
    """
    根据 account_ids 批量获取任务
    仅返回有任务的账户，无任务的账户不包含在结果中
    """
    tasks = await task_manager.get_tasks_by_account_ids(account_ids=request.account_ids)
    return tasks


@router.get("/api/sales-agent/contact/{contact_id}/enrich/status")
async def get_contact_enrich_status(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息请求状态

    Return:
    {
        "status": "pending", # 状态: not_found, pending, success, failed
        "updated_at": "2025-06-09T13:27:39.687886" # 更新时间
    }
    """
    contact_enrichment_status = contact_enrichment_requests.get_contact_enrichment_status(contact_id)
    if contact_enrichment_status is None:
        return {"status": "not_found"}
    return {
        "status": contact_enrichment_status["status"],
        "updated_at": contact_enrichment_status["updated_at"],
    }


@router.get("/api/sales-agent/contact/{contact_id}/enrich")
async def enrich_contact_info(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息（本接口仅返回请求状态）

    Return:
    {
        "status": "pending", # 状态: not_found, pending, success, failed
        "updated_at": "2025-06-09T13:27:39.687886" # 更新时间
    }
    """
    # 校验是否正在获取中
    contact_enrichment_status = contact_enrichment_requests.get_contact_enrichment_status(contact_id)
    if contact_enrichment_status and contact_enrichment_status.get("status", None) == "pending":
        # 扩充请求未完成，不允许重复请求
        raise HTTPException(
            status_code=400,
            detail={"error": "pending", "messages": "Contact is being enriched"},
        )

    # 从zoho中获取联系人信息
    contact_info = await get_contact_by_id(contact_id)
    if contact_info is None:
        # 联系人不存在
        raise HTTPException(status_code=400, detail={"error": "not_found", "messages": "Contact not found"})

    # 如果联系人中没有linkedin或email信息，则直接返回异常
    if contact_info.get("linkedIn", None) is None and contact_info.get("email", None) is None:
        # 联系人中没有linkedin或email信息
        raise HTTPException(
            status_code=400,
            detail={"error": "not_found", "messages": "Contact's LinkedIn URL or Email not found"},
        )

    # 获取联系人电话信息
    request_id = enrich_people_phone(
        contact_id=contact_id,
        linkedin_url=contact_info.get("linkedIn", None),
        email=contact_info.get("email", None),
    )
    if request_id is None:
        # 扩充请求失败
        raise HTTPException(
            status_code=400,
            detail={"error": "failed", "messages": "Failed to enrich contact"},
        )

    # 更新联系人扩充状态为 pending
    contact_enrichment_status = contact_enrichment_requests.update_contact_enrichment_status(
        contact_id, request_id, "pending"
    )

    return contact_enrichment_status


@router.post("/api/sales-agent-webhook/apollo/{request_id}")
async def handle_webhook(request_id: str, request: dict):
    """处理联系人扩充信息的webhook回调数据"""

    # 需要处理联系人webhook回调数据
    contact_id = contact_enrichment_requests.get_contact_id_by_request_id(request_id)
    logger.info(f"request_id: {request_id} contact_id: {contact_id} 收到 webhook 回调数据: {request}")
    if contact_id is None:
        logger.warning(f"request_id: {request_id} 不存在，无法处理webhook回调数据")
        return

    status = "failed"
    try:
        people_list = request.get("people", [])
        if not people_list or len(people_list) == 0:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            return

        contact_info = {}
        data = people_list[0]

        # 解析联系人电话信息
        if data.get("status", "") == "success":
            phone_numbers = data.get("phone_numbers", [])
            for phone_number in phone_numbers:
                if phone_number.get("type_cd", "") == "mobile":
                    contact_info["Mobile"] = phone_number.get("sanitized_number", "")
                else:
                    contact_info["Phone"] = phone_number.get("sanitized_number", "")
            """
            定义 CRM 需要的字段
            "Title": "Title",
            "Email": "<EMAIL>",
			"Secondary_Email": "<EMAIL>"
			"Phone": "988844559",
			"Mobile": "988844559",
			"Home_Phone": "988844559",
			"Other_Phone": "988844559",
            "Department": "Department",
            "LinkedIn": "LinkedIn",
			"Twitter": "Twitter",
			"Facebook": "Facebook",
			"Other_State": "Other_State",
			"First_Name": "First_Name",
			"Asst_Phone": "988844559",
			"Mailing_Street": "Mailing_Street",
			"Mailing_State": "Mailing_State",
			"Mailing_City": "Mailing_City",
			"Mailing_Zip": "Mailing_Zip",
            """

        if contact_info.get("Mobile", None) is None and contact_info.get("Phone", None) is None:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            return

        try:
            # 调crm接口更新联系人扩充信息
            await update_contact_by_id(contact_id, contact_info)
            status = "success"
        except Exception as e:
            logger.error(f"调用 zoho-api 更新联系人 {contact_id} 信息失败: {e}", exc_info=True)

    except Exception as e:
        logger.error(f"处理联系人 {contact_id} 扩充信息的webhook回调数据失败: {e}", exc_info=True)

    finally:
        # 更新联系人扩充状态
        contact_enrichment_requests.update_contact_enrichment_status(contact_id, request_id, status)


"""
从 Apollo 获取到的联系人扩充信息
{
  "person": {
    "first_name": "Tim",
    "last_name": "Zheng",
    "name": "Tim Zheng",
    "linkedin_url": "http://www.linkedin.com/in/tim-zheng-677ba010",
    "title": "Founder & CEO",
    "email_status": "verified",
    "photo_url": "https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2",
    "twitter_url": null,
    "github_url": null,
    "facebook_url": null,
    "extrapolated_email_confidence": null,
    "headline": "Founder & CEO at Apollo",
    "email": "<EMAIL>",
    "organization_id": "5e66b6381e05b4008c8331b8",

    "state": "California",
    "city": "San Francisco",
    "country": "United States",
    "contact_id": "664fa05cf8299f0001f90876",
    "contact": {
      "contact_roles": [],
      "id": "664fa05cf8299f0001f90876",
      "first_name": "Roy",
      "last_name": "Chung",
      "name": "Roy Chung",
      "linkedin_url": "http://www.linkedin.com/in/tim-zheng-677ba010",
      "title": "Reaching Peak Potential 💪⛰️📈🧪️ | President",
      "contact_stage_id": "6095a710bd01d100a506d4ae",
      "owner_id": null,
      "creator_id": "66302798d03b9601c7934ec2",
      "person_id": "66b8a5d38d90c000011cce51",
      "email_needs_tickling": null,
      "organization_name": "Apollo.io",
      "source": "crm",
      "original_source": "crm",
      "organization_id": "5e66b6381e05b4008c8331b8",
      "headline": "Reaching Peak Potential 💪⛰️📈🧪️ | President at FRC",
      "photo_url": null,
      "present_raw_address": "New York, New York, United States",
      "linkedin_uid": null,
      "extrapolated_email_confidence": null,
      "salesforce_id": null,
      "salesforce_lead_id": null,
      "salesforce_contact_id": null,
      "salesforce_account_id": null,
      "crm_owner_id": null,
      "created_at": "2024-05-23T20:00:28.527Z",
      "emailer_campaign_ids": [],
      "direct_dial_status": null,
      "direct_dial_enrichment_failed_at": null,
      "email_status": "verified",
      "email_source": null,
      "account_id": "6658955877a2f20001c648ac",
      "last_activity_date": null,
      "hubspot_vid": null,
      "hubspot_company_id": null,
      "crm_id": null,
      "sanitized_phone": "+***********",
      "merged_crm_ids": null,
      "updated_at": "2024-06-02T08:53:51.266Z",
      "queued_for_crm_push": null,
      "suggested_from_rule_engine_config_id": null,
      "email_unsubscribed": null,
      "label_ids": [],
      "has_pending_email_arcgate_request": false,
      "has_email_arcgate_request": false,
      "existence_level": "invisible",
      "email": "<EMAIL>",
      "email_from_customer": true,
      "typed_custom_fields": {},
      "custom_field_errors": null,
      "crm_record_url": null,
      "email_status_unavailable_reason": null,
      "email_true_status": "Verified",
      "updated_email_true_status": false,
      "contact_rule_config_statuses": [],
      "source_display_name": "Imported from CRM",
      "contact_emails": [
        {
          "email": "<EMAIL>",
          "email_md5": "879440a4afe6515e2de11dd7c531b770",
          "email_sha256": "354f0caf2a603f6bd8e1646693ad829615254584fd83692766ac2db3aaa58e0f",
          "email_status": "verified",
          "email_source": null,
          "extrapolated_email_confidence": null,
          "position": 0,
          "email_from_customer": null,
          "free_domain": false
        }
      ],
      "time_zone": "America/Los_Angeles",
      "phone_numbers": [
        {
          "raw_number": "(*************",
          "sanitized_number": "+***********",
          "type": null,
          "position": 0,
          "status": "valid_number",
          "dnc_status": null,
          "dnc_other_info": null,
          "dialer_flags": null
        },
        {
          "raw_number": "(*************",
          "sanitized_number": "+11234561234",
          "type": null,
          "position": 1,
          "status": "valid_number",
          "dnc_status": null,
          "dnc_other_info": null,
          "dialer_flags": null
        },
        {
          "raw_number": "******-763-6055",
          "sanitized_number": "+***********",
          "type": null,
          "position": 2,
          "status": "valid_number",
          "dnc_status": null,
          "dnc_other_info": null,
          "dialer_flags": {
            "country_name": "United States",
            "country_enabled": true,
            "high_risk_calling_enabled": false,
            "potential_high_risk_number": false
          }
        }
      ],
      "account_phone_note": null,
      "free_domain": false,
      "is_likely_to_engage": false
    },
    "revealed_for_current_team": true,
    "organization": {
      "id": "5e66b6381e05b4008c8331b8",
      "name": "Apollo.io",
      "website_url": "http://www.apollo.io",
      "blog_url": null,
      "angellist_url": null,
      "linkedin_url": "http://www.linkedin.com/company/apolloio",
      "twitter_url": "https://twitter.com/meetapollo/",
      "facebook_url": "https://www.facebook.com/MeetApollo",
      "primary_phone": {},
      "languages": [],
      "alexa_ranking": 3514,
      "phone": null,
      "linkedin_uid": "********",
      "founded_year": 2015,
      "publicly_traded_symbol": null,
      "publicly_traded_exchange": null,
      "logo_url": "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/66d13c8d98ec9600013525b8/picture",
      "crunchbase_url": null,
      "primary_domain": "apollo.io",
      "industry": "information technology & services",
      "keywords": [
        "sales engagement",
        "lead generation",
        "predictive analytics",
        "lead scoring",
        "sales strategy",
        "conversation intelligence",
        "sales enablement",
        "lead routing",
        "sales development",
        "email engagement",
        "revenue intelligence",
        "sales operations",
        "sales intelligence",
        "lead intelligence",
        "prospecting",
        "b2b data"
      ],
      "estimated_num_employees": 1600,
      "industries": [
        "information technology & services"
      ],
      "secondary_industries": [],
      "snippets_loaded": true,
      "industry_tag_id": "5567cd4773696439b10b0000",
      "industry_tag_hash": {
        "information technology & services": "5567cd4773696439b10b0000"
      },
      "retail_location_count": 0,
      "raw_address": "415 Mission St, Floor 37, San Francisco, California 94105, US",
      "street_address": "415 Mission St",
      "city": "San Francisco",
      "state": "California",
      "postal_code": "94105-2301",
      "country": "United States",
      "owned_by_organization_id": null,
      "seo_description": "Search, engage, and convert over 275 million contacts at over 73 million companies with Apollo's sales intelligence and engagement platform.",
      "short_description": "Apollo.io combines a buyer database of over 270M contacts and powerful sales engagement and automation tools in one, easy to use platform. Trusted by over 160,000 companies including Autodesk, Rippling, Deel, Jasper.ai, Divvy, and Heap, Apollo has more than one million users globally. By helping sales professionals find their ideal buyers and intelligently automate outreach, Apollo helps go-to-market teams sell anything.\n\nCelebrating a $100M Series D Funding Round 🦄",
      "suborganizations": [],
      "num_suborganizations": 0,
      "annual_revenue_printed": "100M",
      "annual_revenue": 100000000,
      "total_funding": 251200000,
      "total_funding_printed": "251.2M",
      "latest_funding_round_date": "2023-08-01T00:00:00.000+00:00",
      "latest_funding_stage": "Series D",
      "technology_names": [
        "AI",
        "Android",
        "Basis",
        "Canva",
        "Circle",
        "CloudFlare Hosting",
        "Cloudflare DNS",
        "Drift",
        "Gmail",
        "Google Apps",
        "Google Tag Manager",
        "Google Workspace",
        "Gravity Forms",
        "Hubspot",
        "Intercom",
        "Mailchimp Mandrill",
        "Marketo",
        "Microsoft Office 365",
        "Mobile Friendly",
        "Python",
        "Rackspace MailGun",
        "Remote",
        "Render",
        "Reviews",
        "Salesforce",
        "Stripe",
        "Typekit",
        "WP Engine",
        "Wistia",
        "WordPress.org",
        "Yandex Metrica",
        "reCAPTCHA"
      ],

    },
    "is_likely_to_engage": true,
    "intent_strength": null,
    "show_intent": false,
    "departments": [
      "c_suite"
    ],
    "subdepartments": [
      "executive",
      "founder"
    ],
  }
}
"""
