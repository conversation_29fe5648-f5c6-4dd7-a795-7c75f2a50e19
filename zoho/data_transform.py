from typing import Any, Dict, List

from loguru import logger

from utils.file_handler import load_json_file
from zoho.summarize_info import summarize_apollo_user_info

# 部门列表
departments_list = load_json_file("zoho/departments.json")


def get_twitter_name(twitter_url: str) -> str:
    # Handle None, empty strings, and NaN values
    if not twitter_url:
        return ""

    # Check for NaN as string
    if isinstance(twitter_url, str) and twitter_url.lower() == "nan":
        return ""

    # Check for NaN as float
    if isinstance(twitter_url, float) and str(twitter_url).lower() == "nan":
        return ""

    # Convert to string if not already
    twitter_url = str(twitter_url)

    # 如果 twitter_url 包含 https://twitter.com/xxxyyy 则返回 xxxyyy
    if "https://twitter.com/" in twitter_url:
        return twitter_url.split("/")[-1]
    # 如果 twitter_url 包含 https://x.com/xxxyyy 则返回 xxxyyy
    elif "https://x.com/" in twitter_url:
        return twitter_url.split("/")[-1]
    return ""


# 确保所有值都是字符串类型，如果是 None、NaN、不存在或空值则返回空字符串
def safe_str(value: Any) -> str:
    # Handle None, non-existent values
    if value is None:
        return ""

    # Handle NaN values (both float NaN and string 'nan')
    if isinstance(value, float):
        if str(value).lower() == "nan":
            return ""
    elif isinstance(value, str):
        if value.lower() == "nan":
            return ""

    # Convert to string and strip whitespace
    return str(value).strip()


def get_department(apollo_contact_data: Dict[str, Any]) -> str:
    """获取部门"""
    results = []
    value_to_department = {d.get("value"): d for d in departments_list}
    departments = eval(apollo_contact_data.get("departments", "[]"))
    if departments and len(departments) > 0:
        for department_value in departments:
            department = value_to_department.get(department_value)
            if not department:
                continue
            results.append(department.get("label"))

    return ", ".join(results) if results else ""


async def transform_apollo_contact_data_to_zoho_contact_data(
    apollo_contact_data: Dict[str, Any],
    default_data: dict,
):
    try:
        """将 Apollo 的联系人数据转换为 Zoho 的联系人数据"""
        default_data_extended = {
            **default_data,
            **{
                "Newsletter_Signup": "-None-",
                "Country_Territory": "-None-",
                "Exchange_Rate": 1,
            },
        }

        # Safely get values with defaults for non-existent keys
        temp_data = {
            "First_Name": safe_str(apollo_contact_data.get("first_name", "")),
            "Last_Name": safe_str(apollo_contact_data.get("last_name", "")),
            "Email": safe_str(apollo_contact_data.get("email", "")),
            "Phone": safe_str(apollo_contact_data.get("phone_number", "")),
            "Title": safe_str(apollo_contact_data.get("title", "")),
            "Department": get_department(apollo_contact_data),
            "Mobile": safe_str(apollo_contact_data.get("mobile_number", "")),
            "Function_Type": "Business Management",  # need llm
            "Secondary_Email": "",
            "LinkedIn": safe_str(apollo_contact_data.get("linkedin_url", "")),
            "Currency": "USD",
            "Labels": "Sales Agent",
            "Associated_Account_Type": "-None-",  # need llm
            "Associated_Industry": "-None-",  # need llm
            "Lead_Source": "AI Research",  # 实际上应该是 Agent
            "Twitter": get_twitter_name(safe_str(apollo_contact_data.get("twitter_url", ""))),
            "Facebook": safe_str(apollo_contact_data.get("facebook_url", "")),
            "Region": safe_str(apollo_contact_data.get("country", "")),
            "Rec_Reason": safe_str(apollo_contact_data.get("thinking", "")),
            "Level": safe_str(apollo_contact_data.get("priority", "medium")),
            "Notes": {
                "Title": "Reason For Recommendation",
                "Content": safe_str(apollo_contact_data.get("thinking", "")),
            },
            "Layout": {"id": "3091799000000091033"},
        }
        summarized_data = await summarize_apollo_user_info(
            {**apollo_contact_data, "Department": get_department(apollo_contact_data)}
        )

        # Combine all data
        result_data = {**default_data_extended, **temp_data, **summarized_data}

        # Final check for any NaN values that might have been missed
        for key, value in result_data.items():
            if isinstance(value, float) and str(value).lower() == "nan":
                result_data[key] = ""
            elif isinstance(value, str) and value.lower() == "nan":
                result_data[key] = ""

        return result_data
    except Exception as e:
        logger.error(f"转换数据失败: {e}")
        return {}


async def transform_apollo_contacts_data_to_zoho_contacts_data(
    apollo_contacts_data: List[Dict[str, Any]],
    default_data: dict,
):
    """将 Apollo 的联系人数据转换为 Zoho 的联系人数据"""
    zoho_contacts_data = []
    if apollo_contacts_data is None or len(apollo_contacts_data) == 0:
        return zoho_contacts_data

    for contact in apollo_contacts_data:
        # Process each contact and ensure NaN values are handled
        processed_contact = await transform_apollo_contact_data_to_zoho_contact_data(contact, default_data)
        zoho_contacts_data.append(processed_contact)

    return zoho_contacts_data


async def transform_apollo_webhook_data_to_crm_contact_data(apollo_webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将从 Apollo webhook 返回的联系人数据转换为 CRM 联系人信息

    Args:
        apollo_webhook_data: Apollo webhook 返回的单个联系人数据

    Returns:
        Dict[str, Any]: 转换后的 CRM 联系人数据
    """
    try:
        # 初始化 CRM 联系人数据结构
        crm_contact_data = {
            # 基本信息
            "First_Name": safe_str(apollo_webhook_data.get("first_name", "")),
            "Last_Name": safe_str(apollo_webhook_data.get("last_name", "")),
            "Email": safe_str(apollo_webhook_data.get("email", "")),
            "Title": safe_str(apollo_webhook_data.get("title", "")),
            # 联系方式 - 初始化为空，后续从 phone_numbers 解析
            "Phone": "",
            "Mobile": "",
            "Secondary_Email": "",
            # 社交媒体
            "LinkedIn": safe_str(apollo_webhook_data.get("linkedin_url", "")),
            "Twitter": get_twitter_name(safe_str(apollo_webhook_data.get("twitter_url", ""))),
            "Facebook": safe_str(apollo_webhook_data.get("facebook_url", "")),
            # 地理位置
            "Region": safe_str(apollo_webhook_data.get("country", "")),
            # 部门信息
            "Department": get_department(apollo_webhook_data),
        }

        # 解析电话号码信息
        phone_numbers = apollo_webhook_data.get("phone_numbers", [])
        for phone_number in phone_numbers:
            phone_type = phone_number.get("type_cd", "")
            sanitized_number = phone_number.get("sanitized_number", "")

            if phone_type == "mobile":
                crm_contact_data["Mobile"] = sanitized_number
            else:
                # 如果 Phone 字段还没有值，则使用当前号码
                if not crm_contact_data["Phone"]:
                    crm_contact_data["Phone"] = sanitized_number

        # 如果有组织信息，提取相关字段
        organization = apollo_webhook_data.get("organization", {})
        if organization:
            # 可以根据需要添加组织相关的字段映射
            pass

        # 使用 LLM 分析用户信息来完善字段
        try:
            summarized_data = await summarize_apollo_user_info(
                {**apollo_webhook_data, "Department": crm_contact_data["Department"]}
            )
            if summarized_data:
                # 更新 LLM 分析的字段
                crm_contact_data.update(
                    {
                        "Function_Type": summarized_data.get("Function_Type", crm_contact_data["Function_Type"]),
                        "Associated_Account_Type": summarized_data.get(
                            "Associated_Account_Type", crm_contact_data["Associated_Account_Type"]
                        ),
                        "Associated_Industry": summarized_data.get(
                            "Associated_Industry", crm_contact_data["Associated_Industry"]
                        ),
                        "Region": summarized_data.get("Region", crm_contact_data["Region"]),
                        "Department": summarized_data.get("Department", crm_contact_data["Department"]),
                        "Rec_Reason": summarized_data.get("Rec_Reason", crm_contact_data["Rec_Reason"]),
                    }
                )
        except Exception as e:
            logger.warning(f"LLM 分析用户信息失败，使用默认值: {e}")

        logger.info(
            f"Sales Agent 成功转换 Apollo webhook 数据到 CRM 联系人数据: {crm_contact_data.get('First_Name', '')} {crm_contact_data.get('Last_Name', '')}"
        )
        return crm_contact_data

    except Exception as e:
        logger.error(f"Sales Agent 转换 Apollo webhook 数据到 CRM 联系人数据失败: {e}")
        raise e


def transform_apollo_webhook_data_to_crm_contact_info_simple(apollo_webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将从 Apollo webhook 返回的联系人数据转换为 CRM 联系人信息（简化版本，不使用 LLM）
    适用于 webhook 快速处理场景

    Args:
        apollo_webhook_data: Apollo webhook 返回的单个联系人数据

    Returns:
        Dict[str, Any]: 转换后的 CRM 联系人更新信息（仅包含基本字段）
    """
    try:
        # 初始化 CRM 联系人更新数据（仅包含可以直接映射的字段）
        crm_contact_info = {}

        # 基本信息映射
        if apollo_webhook_data.get("first_name"):
            crm_contact_info["First_Name"] = safe_str(apollo_webhook_data.get("first_name"))
        if apollo_webhook_data.get("last_name"):
            crm_contact_info["Last_Name"] = safe_str(apollo_webhook_data.get("last_name"))
        if apollo_webhook_data.get("email"):
            crm_contact_info["Email"] = safe_str(apollo_webhook_data.get("email"))
        if apollo_webhook_data.get("title"):
            crm_contact_info["Title"] = safe_str(apollo_webhook_data.get("title"))

        # 社交媒体信息
        if apollo_webhook_data.get("linkedin_url"):
            crm_contact_info["LinkedIn"] = safe_str(apollo_webhook_data.get("linkedin_url"))
        if apollo_webhook_data.get("twitter_url"):
            twitter_name = get_twitter_name(safe_str(apollo_webhook_data.get("twitter_url")))
            if twitter_name:
                crm_contact_info["Twitter"] = twitter_name
        if apollo_webhook_data.get("facebook_url"):
            crm_contact_info["Facebook"] = safe_str(apollo_webhook_data.get("facebook_url"))

        # 地理位置
        if apollo_webhook_data.get("country"):
            crm_contact_info["Region"] = safe_str(apollo_webhook_data.get("country"))

        # 部门信息
        department = get_department(apollo_webhook_data)
        if department:
            crm_contact_info["Department"] = department

        # 解析电话号码信息
        phone_numbers = apollo_webhook_data.get("phone_numbers", [])
        for phone_number in phone_numbers:
            phone_type = phone_number.get("type_cd", "")
            sanitized_number = phone_number.get("sanitized_number", "")

            if sanitized_number:
                if phone_type == "mobile":
                    crm_contact_info["Mobile"] = sanitized_number
                else:
                    # 如果 Phone 字段还没有值，则使用当前号码
                    if "Phone" not in crm_contact_info:
                        crm_contact_info["Phone"] = sanitized_number

        logger.info(f"Sales Agent 成功转换 Apollo webhook 数据到 CRM 联系人更新信息: {len(crm_contact_info)} 个字段")
        return crm_contact_info

    except Exception as e:
        logger.error(f"Sales Agent 转换 Apollo webhook 数据到 CRM 联系人更新信息失败: {e}")
        raise e


async def test_data():
    user_info = {
        "id": "54a4c4097468693676168261",
        "first_name": "Kate",
        "last_name": "Brittain",
        "name": "Kate Brittain",
        "linkedin_url": "http://www.linkedin.com/in/kate-brittain-14911076",
        "title": "Assistant Manager",
        "email_status": "verified",
        "photo_url": "https://media.licdn.com/dms/image/v2/C4E03AQGTjCGm4o0XCA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1516569393130?e=**********&v=beta&t=991gaR14Go1Yrd9ZaEbl3XrkfuYnL-ScdbhfMarUifY",
        "twitter_url": "",
        "github_url": "",
        "facebook_url": "",
        "extrapolated_email_confidence": "",
        "headline": "Assistant Manager",
        "email": "<EMAIL>",
        "organization_id": "54a11d8569702d7fe6152f01",
        "state": "Queensland",
        "city": "Brisbane",
        "country": "Australia",
        "departments": "['master_operations', 'master_sales']",
        "subdepartments": "['customer_service_support', 'sales']",
        "seniority": "manager",
        "functions": [],
        "intent_strength": "",
        "show_intent": False,
        "email_domain_catchall": False,
        "revealed_for_current_team": True,
        "personal_emails": [],
        "about": "I am a passionate  driven individual looking for that next challenge to further my career.",  # noqa: E501
        "thinking": "她是销售骨干，并担任助理经理，具备销售和管理经验，工作积极主动，能影响采购或设备引进决策，非常适合作为物联网和边缘计算产品的切入点。LinkedIn活跃度较高，有进一步沟通的空间。",  # noqa: E501
    }
    formatted_data = await transform_apollo_contact_data_to_zoho_contact_data(user_info, {})
    logger.info(formatted_data)


async def test_apollo_webhook_data_transform():
    """测试 Apollo webhook 数据转换"""
    # 模拟 Apollo webhook 返回的数据结构
    apollo_webhook_data = {
        "first_name": "Tim",
        "last_name": "Zheng",
        "name": "Tim Zheng",
        "linkedin_url": "http://www.linkedin.com/in/tim-zheng-677ba010",
        "title": "Founder & CEO",
        "email_status": "verified",
        "email": "<EMAIL>",
        "twitter_url": "https://twitter.com/meetapollo/",
        "facebook_url": "https://www.facebook.com/MeetApollo",
        "country": "United States",
        "departments": "['c_suite', 'executive']",
        "subdepartments": "['founder']",
        "seniority": "c_suite",
        "thinking": "He is the founder and CEO of Apollo, a leading sales intelligence platform. As a C-suite executive with deep understanding of sales technology and data analytics, he would be an ideal contact for IoT and edge computing solutions that can enhance sales processes and customer insights.",
        "priority": "high",
        "phone_numbers": [
            {
                "raw_number": "(*************",
                "sanitized_number": "+11234567890",
                "type_cd": "mobile",
                "status": "valid_number",
            },
            {
                "raw_number": "(*************",
                "sanitized_number": "+11234561234",
                "type_cd": "work",
                "status": "valid_number",
            },
        ],
        "organization": {
            "id": "5e66b6381e05b4008c8331b8",
            "name": "Apollo.io",
            "website_url": "http://www.apollo.io",
            "linkedin_url": "http://www.linkedin.com/company/apolloio",
        },
    }

    # 测试完整转换（带 LLM 分析）
    print("=== 测试完整转换（带 LLM 分析）===")
    crm_data = await transform_apollo_webhook_data_to_crm_contact_data(apollo_webhook_data)
    logger.info(f"完整转换后的 CRM 数据: {crm_data}")

    # 测试简化转换（不带 LLM 分析）
    print("\n=== 测试简化转换（不带 LLM 分析）===")
    crm_info = transform_apollo_webhook_data_to_crm_contact_info_simple(apollo_webhook_data)
    logger.info(f"简化转换后的 CRM 更新信息: {crm_info}")


if __name__ == "__main__":
    import asyncio

    # 运行异步函数
    # asyncio.run(test_data())
    asyncio.run(test_apollo_webhook_data_transform())
