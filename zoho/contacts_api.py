from typing import Optional

import httpx
from loguru import logger

from config import (
    MAX_KEEPALIVE_CONNECTIONS,
    ZOHO_API_HOST,
    ZOHO_API_KEY,
)
from zoho.accounts_api import fetch_accounts_info_by_account_id, get_owner_info_by_user_id
from zoho.zoho_browser_api_client import get_zoho_browser_client, parse_zoho_response_data

zoho_client = httpx.AsyncClient(
    base_url=ZOHO_API_HOST,
    timeout=30.0,
    headers={"x-api-key": ZOHO_API_KEY},
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


async def add_contacts_to_zoho(account_id: str, contacts: list[dict], owner_id: Optional[str] = None):
    """
    调用 zoho-api 添加联系人

    Args:
        account_id: 当前发起探查的公司 id
        contacts: 联系人列表
        owner_id: 所有者 id
    """
    if not account_id or not contacts or len(contacts) == 0 or not owner_id:
        return

    owner_info = await get_owner_info_by_user_id(owner_id)
    if not owner_info:
        raise Exception("Failed to get owner info")

    try:
        payload = {
            "account_id": account_id,
            "data": {
                "total": len(contacts),
                "type": "data_transform",
                "data": contacts,
            },
            "owner_id": owner_id,  # 这里的 owner_id 是示例，请替换为实际的 owner_id
        }
        response = await zoho_client.post("/api/plm/common/contacts/flow", json=payload)

        if response.status_code != 200:
            logger.error(f"调用 zoho-api 添加联系人失败 - 状态码: {response.status_code}, 响应: {response.text}")
            raise Exception(
                f"add contacts to zoho failed - status code: {response.status_code}, response: {response.text}"
            )

    except Exception as e:
        logger.error(f"调用 zoho-api 添加联系人发生异常: {e}")
        raise e


async def update_contact_notes_to_zoho_use_origin_api(contact_id: str, notes: dict, request_headers: dict = {}):
    """
    调用 zoho-api 更新联系人 Notes
    """
    if not contact_id or not notes:
        return
    try:
        payload = {
            "data": [
                {
                    **notes,
                    "Note_Content": notes.get("Content", ""),
                    "Note_Title": notes.get("Title", ""),
                    "Parent_Id": {"id": contact_id},
                    "$se_module": "Contacts",
                    "$attachments": [],
                    "$is_shared_to_client": False,
                }
            ],
            "skip_mandatory": False,
        }
        browser_client = get_zoho_browser_client()
        response = await browser_client.post(
            f"/crm/v9/Contacts/{contact_id}/Notes", json_data=payload, headers=request_headers
        )
        response_data = await parse_zoho_response_data(response)
        if not response_data.success:
            raise Exception(response_data.error_message())

        logger.info(f"update contact notes to zoho success, contact id: {contact_id}, notes: {notes}")
    except Exception as e:
        logger.error(f"调用 zoho-api 更新联系人 Notes 发生异常: {e}")
        raise e


async def add_contact_and_update_notes_to_zoho_use_origin_api(
    account_id: str, contact: dict, owner_id: Optional[str] = None, request_headers: dict = {}
):
    """
    调用 zoho-api 添加联系人, 并更新联系人 Notes
    """
    if not account_id or not contact or not owner_id:
        return
    try:
        payload = {"data": [contact], "skip_mandatory": False}
        browser_client = get_zoho_browser_client()
        response = await browser_client.post("/crm/v2.2/Contacts", json_data=payload, headers=request_headers)

        # 解析响应数据
        response_data = await parse_zoho_response_data(response)
        if not response_data.success:
            raise Exception(response_data.error_message("contact"))

        try:
            contact_id = response_data.data.get("details", {}).get("id")
            if not contact_id:
                raise Exception("contact id is None")

            await update_contact_notes_to_zoho_use_origin_api(contact_id, contact.get("Notes", {}), request_headers)
            logger.info(f"update contact notes success, contact id: {contact_id}, notes: {contact.get('Notes')}")
        except Exception as e:
            logger.warning(f"更新联系人Notes失败，但不影响联系人添加任务 - contact_id: {contact_id}, error: {e}")
            # 继续执行，不抛出异常
    except Exception as e:
        logger.error(f"调用 zoho-api 添加联系人发生异常: {e}")
        raise e


async def add_contacts_to_zoho_use_origin_api(
    account_id: str, contacts: list[dict], owner_id: Optional[str] = None, request_headers: dict = {}
) -> list[dict]:
    """
    调用 zoho-api 添加联系人

    Returns:
        list[dict]: 添加成功的联系人列表
    """
    if not account_id or not contacts or len(contacts) == 0 or not owner_id:
        return []

    account_info = await fetch_accounts_info_by_account_id(account_id)
    if not account_info:
        raise Exception("Failed to get account info")

    try:
        # 循环添加 contacts, 并更新 Notes，
        # 如果添加失败，则跳过，继续添加下一个，
        # 直到所有联系人添加完成，
        # 同时统计添加成功的联系人数量
        #  "Account_Name": {
        #     "id": "3091799000303263001",
        #     "name": "Champion Compressors"
        # },
        success_contacts = []
        for contact in contacts:
            try:
                await add_contact_and_update_notes_to_zoho_use_origin_api(
                    account_id=account_id,
                    contact={
                        **contact,
                        "Account_Name": {"id": account_info.get("id"), "name": account_info.get("name")},
                    },
                    owner_id=owner_id,
                    request_headers=request_headers,
                )
                success_contacts.append(contact)
            except Exception as e:
                logger.error(f"调用 zoho-api 添加联系人发生异常: {e}")
                continue

        return success_contacts

    except Exception as e:
        logger.error(f"调用 zoho-api 添加联系人发生异常: {e}")
        raise e


async def get_contact_by_id(contact_id: str) -> dict | None:
    """调用 zoho-api 获取联系人信息"""
    try:
        response = await zoho_client.get(f"/api/crm/zoho/module/contacts/{contact_id}")

        if response.status_code == 200:
            results = response.json()
            return results.get("result", None)
        else:
            logger.error(f"调用 zoho-api 获取联系人信息失败 - 状态码: {response.status_code}, 响应: {response.text}")
    except Exception as e:
        logger.error(f"调用 zoho-api 获取联系人信息发生异常: {e}")

    return None


async def update_contact_by_id(contact_id: str, contact_info: dict):
    """调用 zoho-api 更新联系人信息"""
    response = await zoho_client.put(f"/api/crm/zoho/module/contacts/{contact_id}", json=contact_info)
    if response.status_code == 200:
        return  # ignore response
    else:
        raise Exception(f"调用 zoho-api 更新联系人信息失败 - 状态码: {response.status_code}, 响应: {response.text}")


# 测试 unassign contact readd to account
async def unassign_contact_and_readd_to_account():
    """
    测试 unassign contact readd to account
    仅供测试使用
    """
    account_id = "3091799000301445003"
    owner_id = "*********"
    contacts = [
        {
            "First_Name": "Bahar",
            "Last_Name": "Faridani",
            "Email": "<EMAIL>",
            "Phone": "",
            "Title": "Public Relations Coordinator | Founder",
            "Department": "C-Suite, Marketing",
            "Mobile": "",
            "Function_Type": "Business Senior Management",
            "Secondary_Email": "",
            "LinkedIn": "http://www.linkedin.com/in/bahar-faridani-8b54ba260",
            "Currency": "USD",
            "Labels": "Sales Agent",
            "Associated_Account_Type": "EU(End User)",
            "Associated_Industry": "ICT",
            "Lead_Source": "LinkedIn",
            "Twitter": "",
            "Facebook": "",
            "Region": "APAC",
            "Rec_Reason": "Bahar Faridani is the founder and public relations coordinator of SpinTel in Australia, holding ultimate decision-making authority, especially for technology and business-related solutions. Her leadership role and deep engagement with company operations make her a primary decision maker for introducing new industrial IoT and communication solutions.",  # noqa: E501
            "Level": "highest",
            "Notes": {
                "Title": "Reason For Recommendation",
                "Content": "Bahar Faridani is the founder and public relations coordinator of SpinTel in Australia, holding ultimate decision-making authority, especially for technology and business-related solutions. Her leadership role and deep engagement with company operations make her a primary decision maker for introducing new industrial IoT and communication solutions.",  # noqa: E501
            },
        }
    ]
    logger.info(f"unassign contact and readd to account: {account_id}")
    logger.info(f"contacts: {contacts}")
    logger.info(f"owner_id: {owner_id}")
    await add_contacts_to_zoho(account_id=account_id, contacts=contacts, owner_id=owner_id)


async def update_notes_to_zoho():
    """
    测试更新联系人Notes
    """
    contact_id = "3091799000310777001"
    notes = {
        "Title": "Reason For Recommendation",
        "Content": "TEST",  # noqa: E501
    }
    await update_contact_notes_to_zoho_use_origin_api(contact_id, notes)


# 使用示例
if __name__ == "__main__":
    import asyncio

    asyncio.run(update_notes_to_zoho())
    # import json

    # contact_id = "3091799000299653001"
    # contact_info = get_contact_by_id(contact_id)
    # print(json.dumps(contact_info, indent=4))
    # update_contact_by_id(contact_id, {"Phone": "***********", "Mobile": "***********"})
