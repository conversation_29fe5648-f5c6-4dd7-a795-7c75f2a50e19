import json
from typing import Annotated, List, Optional

import httpx
import yaml
from langchain_core.callbacks import dispatch_custom_event
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.prebuilt import InjectedState
from langgraph.types import Command
from loguru import logger
from pydantic import BaseModel, Field

from agent.contacts_agent_state import ContactsAgentState
from config import APOLLO_API_KEY, APOLLO_BASE_URL, MAX_KEEPALIVE_CONNECTIONS

# 创建全局异步客户端
apollo_client = httpx.AsyncClient(
    timeout=30.0,
    headers={"X-Api-Key": APOLLO_API_KEY},
    base_url=APOLLO_BASE_URL,
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


class ApolloPeopleResult(BaseModel):
    pagination: dict = Field(description="分页信息")
    people: List[dict] = Field(description="人员列表")


@tool(parse_docstring=True)
async def search_apollo_people(
    state: Annotated[ContactsAgentState, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    organization_id: str,
    person_titles: Optional[list[str]] = None,
    seniorities: Optional[list[str]] = None,
    locations: Optional[list[str]] = None,
) -> Command:
    """
    Search for people within a specific organization using Apollo API.

    Use this function when you need to find employees or team members within a known company.
    This is ideal for prospecting, research, or building contact lists for specific organizations.

    Args:
        organization_id: The unique identifier of the target company, e.g. "1234567890".
            This must be a valid Apollo organization ID obtained from previous organization searches
        person_titles: List of job titles to search for within the organization.
            People matching ANY of these titles will be included in results.
            Searches are flexible and include similar variations, e.g. "marketing manager"
            will also return "content marketing manager" or "digital marketing manager".
            Must be provided in English only
        seniorities: List of job levels to filter results by. Only include levels relevant
            to your search needs. Available options are "owner", "founder", "c_suite",
            "partner", "vp", "head", "director", "manager", "senior", "entry", "intern".
            For example, searching "vp" will include people with "vice president" titles
        locations: Geographic locations where the people are based. Can include cities,
            US states, or countries. Use this to narrow results to specific regions or
            expand to broader geographic areas

    Returns:
        Command object that updates the state with found people and returns a tool message
        with search results summary
    """  # noqa: E501
    logger.info("正在搜索人员列表...")
    page = 1

    try:
        # 构建请求体
        data = {
            "page": page,
            "per_page": 100,
            "organization_ids": [organization_id],
        }

        # 添加可选参数
        if person_titles:
            data["person_titles"] = person_titles

        if seniorities:
            data["person_seniorities"] = seniorities

        if locations:
            data["person_locations"] = locations

        # 发送请求
        response = await apollo_client.post("/mixed_people/search", json=data)

        # 检查请求是否成功
        if response.status_code == 200:
            result = response.json()
            people = result.get("people", [])

            # 需要排除的联系人
            exclude_linkedin_urls = state["exclude_linkedin_urls"]

            # 处理结果
            simplified_results = []
            for person in people:
                if exclude_linkedin_urls and person.get("linkedin_url") in exclude_linkedin_urls:
                    continue
                simplified_person = {
                    "id": person.get("id", ""),
                    "name": person.get("name", ""),
                    "title": person.get("title", ""),
                    "linkedin_url": person.get("linkedin_url", ""),
                }
                simplified_results.append(simplified_person)

            page_info = result.get("pagination", {})
            total_pages = page_info.get("total_pages", 0)

            if total_pages == 1:
                page_info["total_entries"] = len(simplified_results)

            total_entries = page_info.get("total_entries", 0)
            logger.info(f"共搜索到 {total_entries} 条结果")

            if total_entries > 100:
                error_msg = f"返回结果{total_entries}条，超过100条，请调整搜索条件。"
                return Command(
                    update={
                        "messages": [ToolMessage(content=error_msg, tool_call_id=tool_call_id)],
                    }
                )

            logger.info(f"通过Apollo API搜索到 {total_entries} 个结果, 第 {page} 页, 共 {total_pages} 页")

            # 构建返回消息
            yaml_str = yaml.dump(
                {
                    "pagination": page_info,
                    "people": simplified_results,
                },
                indent=2,
            )
            desc_str = (
                f"总人数: {len(simplified_results)}, 数量太少，建议调整搜索条件。"
                if len(simplified_results) < 20
                else ""
            )
            content = f"```yaml\n{yaml_str}\n```\n{desc_str}"

            dispatch_custom_event("thinking", f"I've found {len(simplified_results)} people from apollo")

            return Command(
                update={
                    "found_people": {person["id"]: person for person in simplified_results if person["id"]},
                    "messages": [ToolMessage(content=content, tool_call_id=tool_call_id)],
                }
            )
        else:
            error_msg = f"Apollo API请求失败 - 状态码: {response.status_code}, 消息: {response.text}"
            logger.error(error_msg)
            return Command(
                update={
                    "messages": [ToolMessage(content=json.dumps({"error": error_msg}), tool_call_id=tool_call_id)],
                }
            )

    except Exception as e:
        error_msg = f"使用Apollo API搜索人员时出错: {str(e)}"
        logger.error(error_msg)
        return Command(
            update={
                "messages": [ToolMessage(content=json.dumps({"error": error_msg}), tool_call_id=tool_call_id)],
            }
        )


@tool
async def search_organization(organization_id: str) -> str:
    """从Apollo数据库中获取指定企业的详细基础信息。

    当需要了解目标企业的基本情况以制定联系人搜索策略或验证企业信息时使用此函数。
    适用于已知企业ID且需要获取企业官网、员工规模、行业分类等基础数据的场景。
    不适用于搜索企业或获取联系人信息的操作。

    Args:
        organization_id: 目标企业在Apollo系统中的唯一标识符，必须是有效的企业ID字符串

    Returns:
        包含企业详细信息的JSON字符串，包含企业官网(website)、员工数量、行业分类、企业规模、
        地理位置、成立时间等Apollo数据库中的基础信息字段
    """
    logger.info(f"正在获取企业详情: {organization_id}")
    try:
        response = await apollo_client.post(f"/mixed_companies/search?organization_ids[]={organization_id}")

        if response.status_code == 200:
            organizations = response.json().get("organizations", [])
            if len(organizations) > 0:
                target_organization = organizations[0]
                target_organization["employees_number"] = await get_organization_employees_number(organization_id)
                return target_organization
            return json.dumps({"error": f"查询不到企业信息: {organization_id}"})
        else:
            error_msg = f"Apollo API请求失败 - 状态码: {response.status_code}, 消息: {response.text}"
            logger.error(error_msg)
            return json.dumps({"error": error_msg})

    except Exception as e:
        error_msg = f"使用Apollo API搜索企业详情时出错: {str(e)}"
        logger.error(error_msg)
        return json.dumps({"error": error_msg})


async def get_organization_employees_number(organization_id: str) -> int:
    """获取目标企业的员工数量（企业规模）"""
    try:
        data = {"page": 1, "per_page": 1, "organization_ids": [organization_id]}
        response = await apollo_client.post("/mixed_people/search", json=data)

        if response.status_code == 200:
            result = response.json()
            return result.get("pagination", {}).get("total_entries", 0)
        else:
            logger.error(f"Apollo API请求失败 - 状态码: {response.status_code}, 消息: {response.text}")
    except Exception as e:
        logger.error(f"使用Apollo API获取企业规模时出错: {str(e)}")

    return 0


if __name__ == "__main__":
    import asyncio

    organization_id = "5a9de318a6da98d935685987"
    result = asyncio.run(search_organization.ainvoke(organization_id))
    print(json.dumps(result, indent=4, ensure_ascii=False))
